package com.wechat.scraper.pdf;

import io.appium.java_client.AppiumBy;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.options.UiAutomator2Options;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.net.URL;
import java.time.Duration;

public class WeChatFileHelper {
    private static final Duration WAIT_TIMEOUT = Duration.ofSeconds(15);
    private AndroidDriver driver;
    private WebDriverWait wait;

    public void initDriver() throws Exception {
        UiAutomator2Options options = new UiAutomator2Options()
                .setUdid("emulator-5554")
                .setNoReset(true)
                .setFullReset(false)
                .setAutoGrantPermissions(true)
                .setAppPackage("com.tencent.mm")
                .setAppActivity(".ui.LauncherUI");

        driver = new AndroidDriver(new URL("http://127.0.0.1:4723"), options);
        wait = new WebDriverWait(driver, WAIT_TIMEOUT);
    }

    public void sendMessageToFileHelper(String message) throws Exception {
        // 点击搜索按钮
        WebElement searchBtn = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.id("com.tencent.mm:id/f8y")));
        searchBtn.click();

        // 输入"文件传输助手"
        WebElement searchInput = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.id("com.tencent.mm:id/cd7")));
        searchInput.sendKeys("文件传输助手");

        // 点击搜索结果中的文件传输助手
        WebElement fileHelper = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.androidUIAutomator("new UiSelector().text(\"文件传输助手\")")));
        fileHelper.click();

        // 点击输入框
        WebElement inputBox = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.id("com.tencent.mm:id/al_")));
        inputBox.click();

        // 输入消息
        inputBox.sendKeys(message);

        // 点击发送按钮
        WebElement sendBtn = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.id("com.tencent.mm:id/anv")));
        sendBtn.click();

        System.out.println("消息已发送到文件传输助手: " + message);
    }

    public void closeDriver() {
        if (driver != null) {
            driver.quit();
        }
    }

    public static void main(String[] args) {
        WeChatFileHelper helper = new WeChatFileHelper();
        try {
            helper.initDriver();
            helper.sendMessageToFileHelper("Hello from Appium!");
            Thread.sleep(2000); // 等待消息发送完成
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            helper.closeDriver();
        }
    }
}