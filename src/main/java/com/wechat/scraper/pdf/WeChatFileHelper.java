package com.wechat.scraper.pdf;

import io.appium.java_client.AppiumBy;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.options.UiAutomator2Options;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.net.URL;
import java.time.Duration;

public class WeChatFileHelper {
    private static final Duration WAIT_TIMEOUT = Duration.ofSeconds(15);
    private AndroidDriver driver;
    private WebDriverWait wait;

    public void initDriver() throws Exception {
        // Try multiple approaches to launch WeChat
        Exception lastException = null;

        // Approach 1: Try with the standard launcher activity
        try {
            initDriverWithActivity(".ui.LauncherUI");
            return;
        } catch (Exception e) {
            lastException = e;
            System.out.println("Failed to launch with .ui.LauncherUI: " + e.getMessage());
        }

        // Approach 2: Try with full activity name
        try {
            initDriverWithActivity("com.tencent.mm.ui.LauncherUI");
            return;
        } catch (Exception e) {
            lastException = e;
            System.out.println("Failed to launch with full activity name: " + e.getMessage());
        }

        // Approach 3: Try without specifying activity (let Android choose)
        try {
            initDriverWithoutActivity();
            return;
        } catch (Exception e) {
            lastException = e;
            System.out.println("Failed to launch without activity: " + e.getMessage());
        }

        // If all approaches fail, throw the last exception
        throw new Exception("Failed to initialize WeChat driver with all approaches. Last error: " +
                          (lastException != null ? lastException.getMessage() : "Unknown error"));
    }

    private void initDriverWithActivity(String activityName) throws Exception {
        UiAutomator2Options options = new UiAutomator2Options()
                .setUdid("emulator-5554")
                .setNoReset(true)
                .setFullReset(false)
                .setAutoGrantPermissions(true)
                .setAppPackage("com.tencent.mm")
                .setAppActivity(activityName);

        driver = new AndroidDriver(new URL("http://127.0.0.1:4723"), options);
        wait = new WebDriverWait(driver, WAIT_TIMEOUT);

        // Verify the app launched successfully
        Thread.sleep(3000); // Give the app time to start
        if (driver.getCurrentPackage() == null || !driver.getCurrentPackage().equals("com.tencent.mm")) {
            throw new Exception("WeChat did not launch successfully");
        }
        System.out.println("WeChat launched successfully with activity: " + activityName);
    }

    private void initDriverWithoutActivity() throws Exception {
        UiAutomator2Options options = new UiAutomator2Options()
                .setUdid("emulator-5554")
                .setNoReset(true)
                .setFullReset(false)
                .setAutoGrantPermissions(true)
                .setAppPackage("com.tencent.mm");
                // No activity specified - let Android choose the default

        driver = new AndroidDriver(new URL("http://127.0.0.1:4723"), options);
        wait = new WebDriverWait(driver, WAIT_TIMEOUT);

        // Verify the app launched successfully
        Thread.sleep(3000); // Give the app time to start
        if (driver.getCurrentPackage() == null || !driver.getCurrentPackage().equals("com.tencent.mm")) {
            throw new Exception("WeChat did not launch successfully");
        }
        System.out.println("WeChat launched successfully without specifying activity");
    }

    public void sendMessageToFileHelper(String message) throws Exception {
        // 点击搜索按钮
        WebElement searchBtn = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.id("com.tencent.mm:id/f8y")));
        searchBtn.click();

        // 输入"文件传输助手"
        WebElement searchInput = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.id("com.tencent.mm:id/cd7")));
        searchInput.sendKeys("文件传输助手");

        // 点击搜索结果中的文件传输助手
        WebElement fileHelper = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.androidUIAutomator("new UiSelector().text(\"文件传输助手\")")));
        fileHelper.click();

        // 点击输入框
        WebElement inputBox = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.id("com.tencent.mm:id/al_")));
        inputBox.click();

        // 输入消息
        inputBox.sendKeys(message);

        // 点击发送按钮
        WebElement sendBtn = wait.until(ExpectedConditions.elementToBeClickable(
                AppiumBy.id("com.tencent.mm:id/anv")));
        sendBtn.click();

        System.out.println("消息已发送到文件传输助手: " + message);
    }

    public void closeDriver() {
        if (driver != null) {
            driver.quit();
        }
    }

    /**
     * Helper method to diagnose WeChat installation and availability
     */
    public static void diagnoseWeChatInstallation() {
        System.out.println("=== WeChat Installation Diagnosis ===");
        try {
            // This would require ADB commands to be run
            System.out.println("To manually check WeChat installation, run these commands:");
            System.out.println("1. adb devices  # Check if emulator is connected");
            System.out.println("2. adb shell pm list packages | grep tencent  # Check if WeChat is installed");
            System.out.println("3. adb shell dumpsys package com.tencent.mm | grep -A 1 'Activity'  # Check available activities");
            System.out.println("4. adb shell am start -n com.tencent.mm/.ui.LauncherUI  # Try launching manually");
            System.out.println("=====================================");
        } catch (Exception e) {
            System.err.println("Error during diagnosis: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        // Show diagnosis information first
        diagnoseWeChatInstallation();
        System.out.println();

        WeChatFileHelper helper = new WeChatFileHelper();
        try {
            System.out.println("Starting WeChat automation...");
            System.out.println("Make sure:");
            System.out.println("1. Android emulator is running on emulator-5554");
            System.out.println("2. Appium server is running on http://127.0.0.1:4723");
            System.out.println("3. WeChat is installed on the emulator");
            System.out.println("4. WeChat is logged in and ready to use");
            System.out.println();

            helper.initDriver();
            helper.sendMessageToFileHelper("Hello from Appium!");
            Thread.sleep(2000); // 等待消息发送完成

            System.out.println("Automation completed successfully!");
        } catch (Exception e) {
            System.err.println("Error occurred during automation:");
            System.err.println("Error message: " + e.getMessage());
            System.err.println();
            System.err.println("Troubleshooting steps:");
            System.err.println("1. Check if Android emulator is running: adb devices");
            System.err.println("2. Check if Appium server is running on port 4723");
            System.err.println("3. Check if WeChat is installed: adb shell pm list packages | grep tencent");
            System.err.println("4. Try launching WeChat manually first");
            System.err.println("5. Check Appium server logs for more details");
            e.printStackTrace();
        } finally {
            helper.closeDriver();
        }
    }
}