package com.wechat.scraper.pdf;

import io.appium.java_client.AppiumBy;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.options.UiAutomator2Options;
import org.openqa.selenium.By;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.net.URL;
import java.time.Duration;
import java.util.Map;

public class LaunchAppByName {
    private static final Duration WAIT_TIMEOUT = Duration.ofSeconds(15);

    public static void main(String[] args) throws Exception {
        UiAutomator2Options options = new UiAutomator2Options()
                .setUdid("SCKFAYO7BUIVHUSG")              // adb devices获取
                .setAppPackage("com.tencent.mm")
                .setAppActivity(".ui.LauncherUI")
                .setNoReset(true)               // 保留登录状态
                .setAutoGrantPermissions(true)   // 自动授权
                .setIgnoreHiddenApiPolicyError(true)
                .setSkipServerInstallation(true)
                .setSkipDeviceInitialization(true);

        AndroidDriver driver = new AndroidDriver(
                new URL("http://127.0.0.1:4723"),
                options
        );

        try {

            driver.activateApp("com.tencent.mm");


        } finally {
            driver.quit();
        }
    }
}
