package com.wechat.scraper.pdf;

import io.appium.java_client.AppiumBy;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.options.UiAutomator2Options;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.net.MalformedURLException;
import java.net.URL;
import java.time.Duration;

public class WeChatAutomationV9 {

    private AndroidDriver driver;
    private WebDriverWait wait;
    private static final Duration DEFAULT_WAIT = Duration.ofSeconds(15);

    @BeforeClass
    public void setUp() throws MalformedURLException {
        UiAutomator2Options options = new UiAutomator2Options()
                .setUdid("SCKFAYO7BUIVHUSG")
                .setPlatformName("Android")
                .setAutomationName("UiAutomator2")
                .setAppPackage("com.tencent.mm")
                .setAppActivity(".ui.LauncherUI")
                .setNoReset(true)
                .setFullReset(false)
                .setAutoGrantPermissions(true)
                .setIgnoreHiddenApiPolicyError(true)
                .setSkipServerInstallation(true)
                .setNewCommandTimeout(Duration.ofSeconds(300));

        driver = new AndroidDriver(new URL("http://127.0.0.1:4723"), options);
        wait = new WebDriverWait(driver, DEFAULT_WAIT);
    }

    @Test
    public void testWeChatBasicOperations() {
        // Wait for WeChat to launch
        waitForElement((AppiumBy) AppiumBy.accessibilityId("微信"));

        // Navigate to "Me" tab
        clickElement((AppiumBy) AppiumBy.xpath("//*[@text='我']"));

        // Go to Settings
        clickElement((AppiumBy) AppiumBy.xpath("//*[@text='设置']"));

        // Return to main screen
        driver.navigate().back();
        driver.navigate().back();

        // Go to Contacts tab
        clickElement((AppiumBy) AppiumBy.xpath("//*[@text='通讯录']"));

        // Search for a contact
        clickElement((AppiumBy) AppiumBy.id("com.tencent.mm:id/f3y")); // Search button
        sendKeys((AppiumBy) AppiumBy.id("com.tencent.mm:id/bhn"), "Test Contact"); // Search field

        // Open chat with contact
        clickElement((AppiumBy) AppiumBy.xpath("//*[@text='文件传输助手']"));

        // Send a message
        sendKeys((AppiumBy) AppiumBy.id("com.tencent.mm:id/amr"), "Hello from Appium 9.5.0");
        clickElement((AppiumBy) AppiumBy.id("com.tencent.mm:id/amv")); // Send button
    }

    // Helper methods
    private void waitForElement(AppiumBy locator) {
        wait.until(ExpectedConditions.presenceOfElementLocated(locator));
    }

    private void clickElement(AppiumBy locator) {
        waitForElement(locator);
        driver.findElement(locator).click();
    }

    private void sendKeys(AppiumBy locator, String text) {
        waitForElement(locator);
        WebElement element = driver.findElement(locator);
        element.clear();
        element.sendKeys(text);
    }

    @AfterClass
    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }
}
