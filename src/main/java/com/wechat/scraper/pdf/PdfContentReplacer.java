package com.wechat.scraper.pdf;

import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.pdfwriter.ContentStreamWriter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.graphics.color.PDColor;
import org.apache.pdfbox.pdmodel.graphics.color.PDDeviceRGB;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;

import java.awt.geom.Rectangle2D;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.awt.image.BufferedImage;

public class PdfContentReplacer {

    public void convertToScannedPdf(String inputFilePath, String outputFilePath) throws IOException {
        try (PDDocument document = PDDocument.load(new File(inputFilePath))) {
            try (PDDocument newDocument = new PDDocument()) {
                PDFRenderer pdfRenderer = new PDFRenderer(document);
                for (int i = 0; i < document.getNumberOfPages(); i++) {
                    BufferedImage bim = pdfRenderer.renderImageWithDPI(i, 300); // Render at 300 DPI
                    PDPage newPage = new PDPage(PDRectangle.A4); // Use A4 size, adjust if needed
                    newDocument.addPage(newPage);

                    try (PDPageContentStream contentStream = new PDPageContentStream(newDocument, newPage)) {
                        PDImageXObject pdImage = LosslessFactory.createFromImage(newDocument, bim);
                        contentStream.drawImage(pdImage, 0, 0, newPage.getMediaBox().getWidth(), newPage.getMediaBox().getHeight());
                    }
                }
                newDocument.save(outputFilePath);
            }
        }
    }

    public void replaceContent(String inputFilePath, String outputFilePath, String contentToReplace) throws IOException {
        try (PDDocument document = PDDocument.load(new File(inputFilePath))) {
            if (document.isEncrypted()) {
                document.setAllSecurityToBeRemoved(true);
            }
            for (int i = 0; i < document.getNumberOfPages(); i++) {
                PDPage page = document.getPage(i);
                PDFTextStripper stripper = new PDFTextStripper() {
                    @Override
                    protected void writeString(String text, List<TextPosition> textPositions) throws IOException {
                        for (TextPosition textPosition : textPositions) {
                            String chars = textPosition.getUnicode();
                            if (text.contains(contentToReplace) && contentToReplace.contains(chars)) {
                                float x = textPosition.getXDirAdj()-113;
                                float y = textPosition.getYDirAdj() +392;
                                float width = textPosition.getWidthDirAdj() +254;
                                float height = textPosition.getHeight() +33;

                                // Draw a white rectangle over the identified text
                                try (PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true)) {
                                    contentStream.setNonStrokingColor(1.0f, 1.0f, 1.0f); // Black color
                                    contentStream.addRect(x, y, width, height);
                                    contentStream.fill();
                                }
                            }
                        }
                        super.writeString(text, textPositions);
                    }
                };
                stripper.setStartPage(i + 1);
                stripper.setEndPage(i + 1);
                stripper.getText(document); // This triggers the writeString method
            }
            document.save(outputFilePath);
        }
    }

    public void replaceContent2(String inputFilePath, String outputFilePath, String contentToReplace) throws IOException {
        try (PDDocument document = PDDocument.load(new File(inputFilePath))) {
            if (document.isEncrypted()) {
                document.setAllSecurityToBeRemoved(true);
            }
            for (int i = 0; i < document.getNumberOfPages(); i++) {
                PDPage page = document.getPage(i);
                PDFTextStripper stripper = new PDFTextStripper() {
                    @Override
                    protected void writeString(String text, List<TextPosition> textPositions) throws IOException {
                        for (TextPosition textPosition : textPositions) {
                            String chars = textPosition.getUnicode();
                            if (text.contains(contentToReplace) && contentToReplace.contains(chars)) {
                                float x = textPosition.getXDirAdj();
                                float y = textPosition.getYDirAdj() +312;
                                float width = textPosition.getWidthDirAdj() +200;
                                float height = textPosition.getHeight() +8;

                                // Draw a white rectangle over the identified text
                                try (PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true)) {
                                    contentStream.setNonStrokingColor(1.0f, 1.0f, 1.0f); // Black color
                                    contentStream.addRect(x, y, width, height);
                                    contentStream.fill();
                                }
                            }
                        }
                        super.writeString(text, textPositions);
                    }
                };
                stripper.setStartPage(i + 1);
                stripper.setEndPage(i + 1);
                stripper.getText(document); // This triggers the writeString method
            }
            document.save(outputFilePath);
        }
    }

    public static void main(String[] args) {
        PdfContentReplacer replacer = new PdfContentReplacer();
        try {
            // Create a dummy PDF for testing

            replacer.replaceContent("c:\\111.pdf", "c:\\output1.pdf", "保险费合计");
            System.out.println("Content replacement complete. Check output.pdf");

            replacer.replaceContent2("c:\\output1.pdf", "c:\\output2.pdf", "方案保费合计");
            System.out.println("Content replacement complete. Check output.pdf");

            // Convert the modified PDF to a scanned version
            replacer.convertToScannedPdf("c:\\output2.pdf", "c:\\scanned_output.pdf");
            System.out.println("PDF converted to scanned version. Check scanned_output.pdf");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
} 