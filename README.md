# 微信立减金账单核销明细爬虫

这是一个用于自动获取微信立减金账单核销明细数据的Java爬虫程序。

## 环境要求

- Java 11 或更高版本
- Maven 3.6 或更高版本
- Chrome 浏览器

## 安装步骤

1. 克隆或下载本项目到本地
2. 在项目根目录下运行 Maven 命令安装依赖：
   ```bash
   mvn clean install
   ```

## 使用方法

1. 在项目根目录下运行以下命令启动程序：
   ```bash
   mvn exec:java -Dexec.mainClass="com.wechat.scraper.WeChatDiscountScraper"
   ```

2. 程序启动后会自动打开Chrome浏览器并访问微信商户登录页面

3. 使用微信扫描二维码进行登录

4. 登录成功后，按回车键继续

5. 根据提示输入要查询的批次号

6. 程序会自动执行以下步骤：
   - 导航到批次查询页面
   - 查询指定批次号
   - 预约批次号
   - 下载报告

7. 下载的报告将保存在项目的 `downloads` 目录下

## 日志

- 程序运行日志会同时输出到控制台和 `scraper.log` 文件中
- 日志文件包含详细的运行信息和错误记录

## 注意事项

- 请确保运行程序时网络连接正常
- 需要有效的微信商户号登录权限
- 建议在运行程序时不要手动操作浏览器
- 如果遇到网络延迟，程序会自动等待（最长30秒）

## 常见问题

1. 如果出现 ChromeDriver 相关错误，请确保：
   - Chrome 浏览器已正确安装
   - 使用的是最新版本的 Chrome 浏览器

2. 如果下载报告失败，请检查：
   - 网络连接是否正常
   - 是否有足够的磁盘空间
   - downloads 目录是否有写入权限 