# 微信立减金账单核销明细爬虫

这是一个用于自动获取微信立减金账单核销明细数据的Java爬虫程序。

## 环境要求

- Java 11 或更高版本
- Maven 3.6 或更高版本
- Chrome 浏览器

## 安装步骤

1. 克隆或下载本项目到本地
2. 在项目根目录下运行 Maven 命令安装依赖：
   ```bash
   mvn clean install
   ```

## 使用方法

1. 在项目根目录下运行以下命令启动程序：
   ```bash
   mvn exec:java -Dexec.mainClass="com.wechat.scraper.WeChatDiscountScraper"
   ```

2. 程序启动后会自动打开Chrome浏览器并访问微信商户登录页面

3. 使用微信扫描二维码进行登录

4. 登录成功后，按回车键继续

5. 根据提示输入要查询的批次号

6. 程序会自动执行以下步骤：
   - 导航到批次查询页面
   - 查询指定批次号
   - 预约批次号
   - 下载报告

7. 下载的报告将保存在项目的 `downloads` 目录下

## 日志

- 程序运行日志会同时输出到控制台和 `scraper.log` 文件中
- 日志文件包含详细的运行信息和错误记录

## 注意事项

- 请确保运行程序时网络连接正常
- 需要有效的微信商户号登录权限
- 建议在运行程序时不要手动操作浏览器
- 如果遇到网络延迟，程序会自动等待（最长30秒）

## WeChat 自动化功能

本项目还包含了 WeChat 自动化功能，用于向文件传输助手发送消息。

### 环境要求（WeChat 自动化）

- Android 模拟器或真机
- Appium Server
- WeChat 应用已安装并登录

### 使用 WeChat 自动化

1. 启动 Android 模拟器（确保设备 ID 为 emulator-5554）
2. 启动 Appium Server：
   ```bash
   appium
   ```
3. 运行 WeChat 自动化：
   ```bash
   mvn exec:java -Dexec.mainClass="com.wechat.scraper.pdf.WeChatFileHelper"
   ```

### WeChat 自动化故障排除

如果遇到 "Activity class does not exist" 错误，请按以下步骤排查：

1. **检查设备连接**：
   ```bash
   adb devices
   ```

2. **检查 WeChat 是否已安装**：
   ```bash
   adb shell pm list packages | grep tencent
   ```

3. **运行诊断脚本**：
   - Windows: 运行 `diagnose-wechat.bat`
   - Linux/Mac: 运行 `./diagnose-wechat.sh`

4. **手动启动 WeChat**：
   ```bash
   adb shell am start -n com.tencent.mm/.ui.LauncherUI
   ```

5. **检查 Appium Server 日志** 查看详细错误信息

### 常见的 WeChat 启动问题

- **Activity 不存在**：WeChat 版本不同可能有不同的 Activity 名称
- **权限问题**：确保 WeChat 有必要的权限
- **应用未安装**：在模拟器上安装 WeChat
- **应用未登录**：确保 WeChat 已登录并可以正常使用

## 常见问题

1. 如果出现 ChromeDriver 相关错误，请确保：
   - Chrome 浏览器已正确安装
   - 使用的是最新版本的 Chrome 浏览器

2. 如果下载报告失败，请检查：
   - 网络连接是否正常
   - 是否有足够的磁盘空间
   - downloads 目录是否有写入权限

3. 如果 WeChat 自动化失败，请检查：
   - Android 模拟器是否正在运行
   - Appium Server 是否在端口 4723 上运行
   - WeChat 是否已安装并登录
   - 运行诊断脚本获取详细信息