#!/bin/bash

echo "===== WeChat Installation Diagnosis ====="
echo

echo "1. Checking connected Android devices..."
adb devices
echo

echo "2. Checking if WeChat is installed..."
adb shell pm list packages | grep tencent
echo

echo "3. Getting WeChat package info..."
adb shell dumpsys package com.tencent.mm | grep -i activity
echo

echo "4. Trying to launch WeChat manually..."
echo "Attempting to launch with .ui.LauncherUI..."
adb shell am start -n com.tencent.mm/.ui.LauncherUI
echo

echo "5. Checking current foreground app..."
sleep 3
adb shell dumpsys window windows | grep -i "mCurrentFocus"
echo

echo "6. Alternative launch methods..."
echo "Trying to launch with intent..."
adb shell am start -a android.intent.action.MAIN -c android.intent.category.LAUNCHER -n com.tencent.mm/.ui.LauncherUI
echo

echo "7. Checking all launcher activities..."
adb shell pm dump com.tencent.mm | grep -i "android.intent.action.MAIN"
echo

echo "===== Diagnosis Complete ====="
echo
echo "If WeChat launched successfully, you should see it on the emulator screen."
echo "If not, check the error messages above for troubleshooting."
echo
